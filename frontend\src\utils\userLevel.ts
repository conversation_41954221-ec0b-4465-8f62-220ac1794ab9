export interface UserLevelBadge {
  label: string;
  variant: 'default' | 'secondary' | 'success' | 'warning' | 'error';
}

export function getUserLevelBadge(level: string): UserLevelBadge {
  const levelMap: Record<string, UserLevelBadge> = {
    free: { label: '免费用户', variant: 'secondary' },
    vip1: { label: 'VIP1', variant: 'default' },
    vip2: { label: 'VIP2', variant: 'warning' },
    vip3: { label: 'VIP3', variant: 'success' },
  };

  return levelMap[level] || { label: level, variant: 'secondary' };
}

export function getUserLevelColor(level: string): string {
  const colorMap: Record<string, string> = {
    free: 'text-gray-600',
    vip1: 'text-blue-600',
    vip2: 'text-yellow-600',
    vip3: 'text-green-600',
  };

  return colorMap[level] || 'text-gray-600';
}

export function getUserLevelName(level: string): string {
  const nameMap: Record<string, string> = {
    free: '免费用户',
    vip1: 'VIP1用户',
    vip2: 'VIP2用户',
    vip3: 'VIP3用户',
  };

  return nameMap[level] || level;
}
