import React, { useState } from 'react';
import { AdminTransitionModal } from '../components/admin/AdminTransitionModal';
import { Button } from '../components/ui/Button';

export function AdminAnimationTest() {
  const [showAnimation, setShowAnimation] = useState(false);
  const [userRole, setUserRole] = useState<'admin' | 'user'>('admin');

  const handleTestAnimation = () => {
    setShowAnimation(true);
  };

  const handleRoleChange = (role: 'admin' | 'user') => {
    setUserRole(role);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          管理员动画测试
        </h1>
        
        <div className="space-y-6">
          {/* 角色选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              测试角色
            </label>
            <div className="flex space-x-4">
              <button
                onClick={() => handleRoleChange('admin')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  userRole === 'admin'
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                管理员
              </button>
              <button
                onClick={() => handleRoleChange('user')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  userRole === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                普通用户
              </button>
            </div>
          </div>

          {/* 当前状态显示 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">当前状态</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <p>角色: <span className={`font-medium ${userRole === 'admin' ? 'text-red-600' : 'text-blue-600'}`}>
                {userRole === 'admin' ? '管理员' : '普通用户'}
              </span></p>
              <p>动画效果: {userRole === 'admin' ? '✅ 启用' : '❌ 禁用'}</p>
            </div>
          </div>

          {/* 测试按钮 */}
          <Button
            onClick={handleTestAnimation}
            className={`w-full ${
              userRole === 'admin'
                ? 'bg-red-600 hover:bg-red-700'
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            测试管理端动画
          </Button>

          {/* 说明文字 */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• 管理员角色：显示完整的权限验证动画流程</p>
            <p>• 普通用户：直接跳转，不显示动画</p>
            <p>• 动画包含4个阶段：扫描→验证→确认→过渡</p>
          </div>
        </div>
      </div>

      {/* 动画模态框 */}
      <AdminTransitionModal
        isOpen={showAnimation}
        onClose={() => setShowAnimation(false)}
        userRole={userRole}
      />
    </div>
  );
}
