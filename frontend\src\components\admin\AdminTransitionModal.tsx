import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface AdminTransitionModalProps {
  isOpen: boolean;
  onClose: () => void;
  userRole: string;
}

type TransitionStage = 'scanning' | 'verifying' | 'confirmed' | 'transitioning' | 'complete';

export function AdminTransitionModal({ isOpen, onClose, userRole }: AdminTransitionModalProps) {
  const navigate = useNavigate();
  const [stage, setStage] = useState<TransitionStage>('scanning');
  const [progress, setProgress] = useState(0);
  const [scanLine, setScanLine] = useState(0);

  useEffect(() => {
    if (!isOpen) {
      // 重置状态
      setStage('scanning');
      setProgress(0);
      setScanLine(0);
      return;
    }

    // 只有管理员才能看到完整动画
    if (userRole !== 'admin') {
      onClose();
      return;
    }

    // 开始动画序列
    startTransitionSequence();
  }, [isOpen, userRole]);

  const startTransitionSequence = async () => {
    try {
      // 阶段1: 权限扫描 (2秒)
      setStage('scanning');

      // 扫描线动画
      const scanInterval = setInterval(() => {
        setScanLine(prev => (prev + 2) % 100);
      }, 50);

      // 进度条动画
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            clearInterval(scanInterval);
            return 100;
          }
          return prev + 1;
        });
      }, 20);

      // 等待扫描完成
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 阶段2: 身份验证 (1秒)
      setStage('verifying');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 阶段3: 身份确认 (1.5秒)
      setStage('confirmed');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 阶段4: 界面过渡 (1秒)
      setStage('transitioning');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 阶段5: 完成跳转
      setStage('complete');

      // 添加一个短暂的完成状态显示
      setTimeout(() => {
        // 只有在实际应用中才跳转，测试页面不跳转
        if (window.location.pathname !== '/test/admin-animation') {
          navigate('/admin');
        }
        onClose();
      }, 300);

    } catch (error) {
      console.error('权限验证动画出错:', error);
      // 出错时直接跳转
      navigate('/admin');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* 动态背景遮罩 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/90 via-purple-900/90 to-red-900/90 backdrop-blur-sm">
        {/* 背景粒子效果 */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full animate-pulse opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* 主要内容 */}
      <div className="relative h-full flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 transform transition-all duration-500 animate-scale-in border border-gray-200">
          
          {/* 扫描阶段 */}
          {stage === 'scanning' && (
            <div className="text-center space-y-6">
              <div className="relative">
                <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-red-500 rounded-full flex items-center justify-center animate-admin-glow">
                  <svg className="w-12 h-12 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                
                {/* 扫描线动画 */}
                <div className="absolute inset-0 rounded-full overflow-hidden">
                  <div 
                    className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-white to-transparent opacity-80 transition-all duration-100"
                    style={{ 
                      top: `${scanLine}%`,
                      transform: 'translateY(-50%)'
                    }}
                  />
                </div>
                
                {/* 脉冲环 */}
                <div className="absolute inset-0 rounded-full border-2 border-blue-400 animate-ping opacity-30" />
                <div className="absolute inset-2 rounded-full border-2 border-red-400 animate-ping opacity-20" style={{ animationDelay: '0.5s' }} />
              </div>
              
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-2 animate-pulse">权限验证中</h3>
                <p className="text-gray-600 text-sm">正在扫描管理员身份...</p>
                <div className="flex justify-center space-x-1 mt-2">
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                      style={{ animationDelay: `${i * 0.2}s` }}
                    />
                  ))}
                </div>
              </div>
              
              {/* 进度条 */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-red-500 h-2 rounded-full transition-all duration-100"
                  style={{ width: `${progress}%` }}
                />
              </div>
              
              <div className="text-xs text-gray-500">
                {progress}% 完成
              </div>
            </div>
          )}

          {/* 验证阶段 */}
          {stage === 'verifying' && (
            <div className="text-center space-y-6">
              <div className="w-24 h-24 mx-auto bg-yellow-500 rounded-full flex items-center justify-center animate-pulse">
                <svg className="w-12 h-12 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">身份验证</h3>
                <p className="text-gray-600 text-sm">正在验证管理员权限...</p>
              </div>
            </div>
          )}

          {/* 确认阶段 */}
          {stage === 'confirmed' && (
            <div className="text-center space-y-6">
              <div className="w-24 h-24 mx-auto bg-green-500 rounded-full flex items-center justify-center animate-bounce">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              
              <div>
                <h3 className="text-xl font-bold text-green-600 mb-2">身份确认成功</h3>
                <p className="text-gray-600 text-sm">管理员权限验证通过</p>
              </div>
              
              {/* 成功粒子效果 */}
              <div className="relative">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-green-400 rounded-full animate-ping"
                    style={{
                      left: `${20 + i * 10}%`,
                      top: `${Math.random() * 20}px`,
                      animationDelay: `${i * 0.2}s`,
                      animationDuration: '1s'
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* 过渡阶段 */}
          {stage === 'transitioning' && (
            <div className="text-center space-y-6">
              <div className="relative">
                <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 via-purple-500 to-red-500 rounded-full flex items-center justify-center animate-spin">
                  <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                  </svg>
                </div>

                {/* 多层波纹效果 */}
                <div className="absolute inset-0 rounded-full">
                  <div className="absolute inset-0 bg-red-100 rounded-full animate-ping opacity-20" />
                  <div className="absolute inset-2 bg-red-200 rounded-full animate-ping opacity-30" style={{ animationDelay: '0.3s' }} />
                  <div className="absolute inset-4 bg-red-300 rounded-full animate-ping opacity-40" style={{ animationDelay: '0.6s' }} />
                </div>
              </div>

              <div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-red-600 bg-clip-text text-transparent mb-2">正在进入管理端</h3>
                <p className="text-gray-600 text-sm">界面切换中...</p>

                {/* 进度指示器 */}
                <div className="flex justify-center space-x-2 mt-4">
                  {[...Array(5)].map((_, i) => (
                    <div
                      key={i}
                      className="w-3 h-3 bg-gradient-to-r from-blue-500 to-red-500 rounded-full animate-pulse"
                      style={{
                        animationDelay: `${i * 0.1}s`,
                        animationDuration: '0.8s'
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 完成阶段 */}
          {stage === 'complete' && (
            <div className="text-center space-y-6">
              <div className="w-24 h-24 mx-auto bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center animate-bounce">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                </svg>
              </div>

              <div>
                <h3 className="text-xl font-bold text-green-600 mb-2">进入成功</h3>
                <p className="text-gray-600 text-sm">正在跳转到管理端...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
